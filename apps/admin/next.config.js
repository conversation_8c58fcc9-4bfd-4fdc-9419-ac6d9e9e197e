/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ['@azkuja/shared'],
  eslint: {
    ignoreDuringBuilds: true,
  },
  experimental: {
    esmExternals: false,
  },
  // Only set output for production export builds
  output:
    process.env.NODE_ENV === 'production' && process.env.BUILD_TYPE === 'export'
      ? 'export'
      : undefined,
  trailingSlash: false,

  // Webpack configuration to resolve lodash-es issues
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Alias lodash-es to regular lodash to resolve module issues
    config.resolve.alias = {
      ...config.resolve.alias,
      'lodash-es': 'lodash',
    };

    return config;
  },

  // Only apply cache headers to API routes and pages, not static assets
  async headers() {
    return [
      {
        source: '/((?!_next/static|favicon.ico).*)',
        headers: [
          {
            key: 'Cache-Control',
            value:
              process.env.NODE_ENV === 'development'
                ? 'no-cache, no-store, must-revalidate'
                : 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
